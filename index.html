<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechFlow - Build Faster. Launch Smarter.</title>
    <meta name="description" content="Revolutionary platform to accelerate your development workflow with AI-powered tools and seamless deployment.">
    <link rel="canonical" href="https://techflow.com">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://techflow.com">
    <meta property="og:title" content="TechFlow - Build Faster. Launch Smarter.">
    <meta property="og:description" content="Revolutionary platform to accelerate your development workflow with AI-powered tools and seamless deployment.">
    <meta property="og:image" content="https://techflow.com/hero.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://techflow.com">
    <meta property="twitter:title" content="TechFlow - Build Faster. Launch Smarter.">
    <meta property="twitter:description" content="Revolutionary platform to accelerate your development workflow with AI-powered tools and seamless deployment.">
    <meta property="twitter:image" content="https://techflow.com/hero.jpg">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚡</text></svg>">

    <!-- JSON-LD Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "TechFlow",
        "url": "https://techflow.com",
        "logo": "https://techflow.com/logo.svg",
        "description": "Revolutionary platform to accelerate your development workflow",
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "******-0123",
            "contactType": "customer service"
        }
    }
    </script>

    <style>
        /* CSS Reset & Variables */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        :root {
            --primary-color: #0F62FE;
            --secondary-color: #111827;
            --accent-color: #22C55E;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
            --font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --transition: all 0.2s ease-in-out;
        }

        /* RTL Support */
        [dir="rtl"] {
            --text-align: right;
            --flex-direction: row-reverse;
        }

        [dir="ltr"] {
            --text-align: left;
            --flex-direction: row;
        }

        /* Base Styles */
        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-primary);
        }

        /* Skip to content */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color);
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 100;
            transition: var(--transition);
        }

        .header.scrolled {
            box-shadow: var(--shadow-lg);
            background: rgba(255, 255, 255, 0.98);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .logo {
            text-decoration: none;
            z-index: 101;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .logo-icon {
            width: 32px;
            height: 32px;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .nav-menu {
            display: flex;
            align-items: center;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: var(--spacing-lg);
            margin: 0;
            padding: 0;
        }

        .nav-link {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color);
            background: var(--bg-secondary);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
            border-radius: 1px;
        }

        .nav-controls {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            z-index: 101;
        }

        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            background: none;
            border: none;
            cursor: pointer;
            padding: var(--spacing-xs);
            gap: 4px;
        }

        .mobile-menu-toggle span {
            width: 24px;
            height: 2px;
            background: var(--text-primary);
            transition: var(--transition);
        }

        .mobile-menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .mobile-menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        /* Language Toggle */
        .lang-toggle {
            background: none;
            border: 1px solid var(--border-color);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .lang-toggle:hover {
            background: var(--bg-secondary);
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #0d52d1;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-secondary:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
            padding: var(--spacing-xl) 0;
            text-align: center;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
            align-items: center;
        }

        .hero h1 {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: bold;
            margin-bottom: var(--spacing-sm);
            color: var(--secondary-color);
        }

        .hero p {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: center;
            flex-wrap: wrap;
        }

        .hero-image {
            width: 100%;
            max-width: 500px;
            height: 300px;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            border-radius: var(--border-radius);
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        /* Trust Section */
        .trust {
            padding: var(--spacing-lg) 0;
            background: var(--bg-primary);
            text-align: center;
        }

        .trust-logos {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
            margin-bottom: var(--spacing-lg);
        }

        .trust-logo {
            width: 120px;
            height: 60px;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
        }

        /* Features Section */
        .features {
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: var(--spacing-lg);
            color: var(--secondary-color);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .feature-card {
            background: var(--bg-primary);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            text-align: center;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-sm);
            color: white;
            font-size: 1.5rem;
        }

        /* Solutions Section */
        .solutions {
            padding: var(--spacing-xl) 0;
            background: var(--bg-primary);
        }

        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-xl);
            margin-top: var(--spacing-lg);
        }

        .solution-card {
            background: var(--bg-secondary);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 1px solid var(--border-color);
        }

        .solution-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
        }

        .solution-image {
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .solution-image svg {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .solution-content {
            padding: var(--spacing-lg);
        }

        .solution-content h3 {
            font-size: 1.5rem;
            margin-bottom: var(--spacing-sm);
            color: var(--secondary-color);
        }

        .solution-content p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
            line-height: 1.6;
        }

        .solution-features {
            list-style: none;
            padding: 0;
        }

        .solution-features li {
            padding: var(--spacing-xs) 0;
            position: relative;
            padding-left: var(--spacing-md);
            color: var(--text-primary);
        }

        .solution-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }

        /* Stats Section */
        .stats {
            padding: var(--spacing-xl) 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: var(--spacing-xs);
            background: linear-gradient(45deg, #fff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 1.125rem;
            opacity: 0.9;
        }

        /* Testimonials Section */
        .testimonials {
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .testimonial-card {
            background: var(--bg-primary);
            padding: var(--spacing-lg);
            border-radius: 12px;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 1px solid var(--border-color);
        }

        .testimonial-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .testimonial-content {
            margin-bottom: var(--spacing-md);
        }

        .stars {
            font-size: 1.25rem;
            margin-bottom: var(--spacing-sm);
        }

        .testimonial-content p {
            font-style: italic;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
        }

        .author-avatar svg {
            width: 100%;
            height: 100%;
        }

        .author-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .author-title {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr 1fr;
                text-align: left;
            }

            .hero-actions {
                justify-content: flex-start;
            }
        }

        @media (max-width: 480px) {
            .nav {
                padding: var(--spacing-xs) var(--spacing-sm);
            }

            .hero {
                padding: var(--spacing-lg) 0;
            }

            .btn {
                padding: var(--spacing-xs) var(--spacing-sm);
                font-size: 0.9rem;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        *:focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* How It Works Section */
        .how-it-works {
            padding: var(--spacing-xl) 0;
            background: var(--bg-primary);
        }

        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .step-card {
            text-align: center;
            padding: var(--spacing-lg);
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto var(--spacing-sm);
        }

        /* Pricing Section */
        .pricing {
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .pricing-card {
            background: var(--bg-primary);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            text-align: center;
            position: relative;
            border: 2px solid transparent;
            transition: var(--transition);
        }

        .pricing-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .pricing-card.featured {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .popular-badge {
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--accent-color);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 600;
        }

        .price {
            font-size: 3rem;
            font-weight: bold;
            color: var(--primary-color);
            margin: var(--spacing-sm) 0;
        }

        .period {
            font-size: 1rem;
            color: var(--text-secondary);
            font-weight: normal;
        }

        .features-list {
            list-style: none;
            margin: var(--spacing-lg) 0;
            text-align: left;
        }

        .features-list li {
            padding: var(--spacing-xs) 0;
            position: relative;
            padding-left: var(--spacing-md);
        }

        .features-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }

        /* FAQ Section */
        .faq {
            padding: var(--spacing-xl) 0;
            background: var(--bg-primary);
        }

        .faq-list {
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            border-bottom: 1px solid var(--border-color);
        }

        .faq-question {
            width: 100%;
            padding: var(--spacing-md) 0;
            background: none;
            border: none;
            text-align: left;
            font-size: 1.125rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
        }

        .faq-question:hover {
            color: var(--primary-color);
        }

        .faq-icon {
            font-size: 1.5rem;
            transition: var(--transition);
        }

        .faq-question[aria-expanded="true"] .faq-icon {
            transform: rotate(45deg);
        }

        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .faq-answer.open {
            max-height: 200px;
            padding-bottom: var(--spacing-md);
        }

        /* Contact Section */
        .contact {
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }

        .contact-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }

        .contact-methods {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-lg);
        }

        .contact-method {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-primary);
            text-decoration: none;
            padding: var(--spacing-sm);
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .contact-method:hover {
            background: var(--bg-primary);
        }

        .contact-icon {
            font-size: 1.25rem;
        }

        /* Form Styles */
        .contact-form {
            background: var(--bg-primary);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-group input {
            width: 100%;
            padding: var(--spacing-sm);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-group input:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .form-group input.error {
            border-color: #ef4444;
        }

        .error-message {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: var(--spacing-xs);
            min-height: 1.25rem;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }

        .checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-sm);
            cursor: pointer;
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .btn-full {
            width: 100%;
        }

        .btn-sm {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: 0.875rem;
        }

        /* Footer */
        .footer {
            background: var(--secondary-color);
            color: white;
            padding: var(--spacing-xl) 0 var(--spacing-lg);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .footer-section h3,
        .footer-section h4 {
            margin-bottom: var(--spacing-sm);
            color: white;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: var(--spacing-xs);
        }

        .footer-section a {
            color: #d1d5db;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-section a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: var(--spacing-md);
            text-align: center;
            color: #9ca3af;
        }

        /* Cookie Banner */
        .cookie-banner {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--secondary-color);
            color: white;
            padding: var(--spacing-md);
            z-index: 1000;
            transform: translateY(100%);
            transition: transform 0.3s ease-out;
        }

        .cookie-banner.show {
            transform: translateY(0);
        }

        .cookie-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            gap: var(--spacing-md);
        }

        .cookie-actions {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .cookie-link {
            color: #d1d5db;
            text-decoration: none;
            font-size: 0.875rem;
        }

        .cookie-link:hover {
            color: white;
        }

        /* Toast */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent-color);
            color: white;
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            transform: translateX(400px);
            transition: transform 0.3s ease-out;
            z-index: 1000;
            max-width: 300px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .toast-icon {
            font-size: 1.25rem;
        }

        /* RTL Adjustments */
        [dir="rtl"] .hero-content {
            text-align: right;
        }

        [dir="rtl"] .hero-actions {
            justify-content: flex-end;
        }

        [dir="rtl"] .features-list li {
            padding-right: var(--spacing-md);
            padding-left: 0;
        }

        [dir="rtl"] .features-list li::before {
            right: 0;
            left: auto;
        }

        [dir="rtl"] .faq-question {
            text-align: right;
        }

        [dir="rtl"] .contact-method {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .checkbox-label {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .cookie-content {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .toast {
            right: auto;
            left: 20px;
            transform: translateX(-400px);
        }

        [dir="rtl"] .toast.show {
            transform: translateX(0);
        }

        /* Integrations Section */
        .integrations {
            padding: var(--spacing-xl) 0;
            background: var(--bg-primary);
        }

        .section-subtitle {
            text-align: center;
            color: var(--text-secondary);
            font-size: 1.125rem;
            margin-bottom: var(--spacing-xl);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .integrations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-lg);
            max-width: 800px;
            margin: 0 auto;
        }

        .integration-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            transition: var(--transition);
            cursor: pointer;
        }

        .integration-item:hover {
            background: var(--bg-secondary);
            transform: translateY(-2px);
        }

        .integration-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .integration-icon svg {
            width: 100%;
            height: 100%;
        }

        .integration-item span {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
        }

        /* Resources Section */
        .resources {
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }

        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .resource-card {
            background: var(--bg-primary);
            padding: var(--spacing-lg);
            border-radius: 12px;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .resource-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .resource-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-sm);
            display: block;
        }

        .resource-card h3 {
            font-size: 1.25rem;
            margin-bottom: var(--spacing-sm);
            color: var(--secondary-color);
        }

        .resource-card p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
            line-height: 1.6;
        }

        .resource-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .resource-link:hover {
            color: var(--secondary-color);
        }

        /* Mobile Responsive Improvements */
        @media (max-width: 768px) {
            .nav-menu {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--bg-primary);
                border-top: 1px solid var(--border-color);
                box-shadow: var(--shadow-lg);
                transform: translateY(-100%);
                opacity: 0;
                visibility: hidden;
                transition: var(--transition);
            }

            .nav-menu.active {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }

            .nav-links {
                flex-direction: column;
                padding: var(--spacing-md);
                gap: 0;
            }

            .nav-link {
                display: block;
                padding: var(--spacing-sm) var(--spacing-md);
                border-radius: var(--border-radius);
                margin-bottom: var(--spacing-xs);
            }

            .mobile-menu-toggle {
                display: flex;
            }

            .solutions-grid,
            .testimonials-grid,
            .resources-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .integrations-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: var(--spacing-md);
            }

            .integration-item {
                padding: var(--spacing-sm);
            }

            .integration-icon {
                width: 40px;
                height: 40px;
            }

            .integration-item span {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .integrations-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* Animation Classes */
        .animate-in {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading State */
        body:not(.loaded) * {
            animation-duration: 0s !important;
            transition-duration: 0s !important;
        }
    </style>
</head>
<body>
    <a href="#main" class="skip-link">Skip to main content</a>
    
    <header class="header" id="header">
        <nav class="nav" role="navigation" aria-label="Main navigation">
            <a href="#" class="logo" aria-label="TechFlow Home">
                <div class="logo-container">
                    <svg class="logo-icon" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="40" height="40" rx="8" fill="url(#gradient)"/>
                        <path d="M12 28L20 12L28 28H24L22 24H18L16 28H12Z" fill="white"/>
                        <defs>
                            <linearGradient id="gradient" x1="0" y1="0" x2="40" y2="40">
                                <stop offset="0%" stop-color="#0F62FE"/>
                                <stop offset="100%" stop-color="#22C55E"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    <span class="logo-text" data-en="TechFlow" data-ar="تك فلو">TechFlow</span>
                </div>
            </a>

            <div class="nav-menu" id="navMenu">
                <ul class="nav-links">
                    <li><a href="#features" class="nav-link" data-en="Features" data-ar="الميزات">Features</a></li>
                    <li><a href="#solutions" class="nav-link" data-en="Solutions" data-ar="الحلول">Solutions</a></li>
                    <li><a href="#testimonials" class="nav-link" data-en="Reviews" data-ar="التقييمات">Reviews</a></li>
                    <li><a href="#pricing" class="nav-link" data-en="Pricing" data-ar="التسعير">Pricing</a></li>
                    <li><a href="#resources" class="nav-link" data-en="Resources" data-ar="الموارد">Resources</a></li>
                </ul>
            </div>

            <div class="nav-controls">
                <button class="lang-toggle" id="langToggle" aria-label="Toggle language">
                    <span data-en="العربية" data-ar="English">العربية</span>
                </button>
                <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <a href="#contact" class="btn btn-primary">
                    <span data-en="Get Started" data-ar="ابدأ الآن">Get Started</span>
                </a>
            </div>
        </nav>
    </header>

    <main id="main">
        <section class="hero" aria-labelledby="hero-title">
            <div class="container">
                <div class="hero-content">
                    <div>
                        <h1 id="hero-title">
                            <span data-en="Build Faster. Launch Smarter." data-ar="ابنِ أسرع. أطلق بذكاء.">Build Faster. Launch Smarter.</span>
                        </h1>
                        <p>
                            <span data-en="Revolutionary platform to accelerate your development workflow with AI-powered tools and seamless deployment." data-ar="منصة ثورية لتسريع سير عمل التطوير مع أدوات مدعومة بالذكاء الاصطناعي ونشر سلس.">Revolutionary platform to accelerate your development workflow with AI-powered tools and seamless deployment.</span>
                        </p>
                        <div class="hero-actions">
                            <a href="#contact" class="btn btn-primary">
                                <span data-en="Start Free Trial" data-ar="ابدأ تجربة مجانية">Start Free Trial</span>
                            </a>
                            <a href="#features" class="btn btn-secondary">
                                <span data-en="Learn More" data-ar="اعرف المزيد">Learn More</span>
                            </a>
                        </div>
                    </div>
                    <div class="hero-image" role="img" aria-label="TechFlow Platform Illustration">
                        ⚡ TechFlow
                    </div>
                </div>
            </div>
        </section>

        <section class="trust" aria-labelledby="trust-title">
            <div class="container">
                <h2 id="trust-title" class="sr-only">
                    <span data-en="Trusted by leading companies" data-ar="موثوق من قبل الشركات الرائدة">Trusted by leading companies</span>
                </h2>
                <div class="trust-logos">
                    <div class="trust-logo" aria-label="Company A">Company A</div>
                    <div class="trust-logo" aria-label="Company B">Company B</div>
                    <div class="trust-logo" aria-label="Company C">Company C</div>
                    <div class="trust-logo" aria-label="Company D">Company D</div>
                </div>
                <p>
                    <span data-en="⭐⭐⭐⭐⭐ Rated 4.9/5 by 10,000+ developers" data-ar="⭐⭐⭐⭐⭐ تقييم 4.9/5 من أكثر من 10,000 مطور">⭐⭐⭐⭐⭐ Rated 4.9/5 by 10,000+ developers</span>
                </p>
            </div>
        </section>

        <section class="features" id="features" aria-labelledby="features-title">
            <div class="container">
                <h2 id="features-title" class="section-title">
                    <span data-en="Why Choose TechFlow?" data-ar="لماذا تختار تك فلو؟">Why Choose TechFlow?</span>
                </h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon" aria-hidden="true">⚡</div>
                        <h3>
                            <span data-en="Lightning Fast" data-ar="سرعة البرق">Lightning Fast</span>
                        </h3>
                        <p>
                            <span data-en="Deploy your applications in seconds, not hours. Our optimized infrastructure ensures maximum performance." data-ar="انشر تطبيقاتك في ثوانٍ وليس ساعات. بنيتنا المحسنة تضمن أقصى أداء.">Deploy your applications in seconds, not hours. Our optimized infrastructure ensures maximum performance.</span>
                        </p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon" aria-hidden="true">🛡️</div>
                        <h3>
                            <span data-en="Enterprise Security" data-ar="أمان المؤسسات">Enterprise Security</span>
                        </h3>
                        <p>
                            <span data-en="Bank-grade security with end-to-end encryption, compliance certifications, and 24/7 monitoring." data-ar="أمان بمستوى البنوك مع تشفير شامل وشهادات امتثال ومراقبة على مدار الساعة.">Bank-grade security with end-to-end encryption, compliance certifications, and 24/7 monitoring.</span>
                        </p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon" aria-hidden="true">🤖</div>
                        <h3>
                            <span data-en="AI-Powered" data-ar="مدعوم بالذكاء الاصطناعي">AI-Powered</span>
                        </h3>
                        <p>
                            <span data-en="Intelligent code suggestions, automated testing, and smart deployment strategies powered by machine learning." data-ar="اقتراحات كود ذكية واختبار آلي واستراتيجيات نشر ذكية مدعومة بالتعلم الآلي.">Intelligent code suggestions, automated testing, and smart deployment strategies powered by machine learning.</span>
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <section class="solutions" id="solutions" aria-labelledby="solutions-title">
            <div class="container">
                <h2 id="solutions-title" class="section-title">
                    <span data-en="Tailored Solutions for Every Team" data-ar="حلول مخصصة لكل فريق">Tailored Solutions for Every Team</span>
                </h2>
                <div class="solutions-grid">
                    <div class="solution-card">
                        <div class="solution-image">
                            <svg viewBox="0 0 200 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="200" height="150" rx="8" fill="#f8fafc"/>
                                <rect x="20" y="20" width="160" height="110" rx="4" fill="url(#solutionGrad1)"/>
                                <circle cx="60" cy="60" r="15" fill="white" opacity="0.8"/>
                                <circle cx="100" cy="80" r="12" fill="white" opacity="0.6"/>
                                <circle cx="140" cy="65" r="18" fill="white" opacity="0.9"/>
                                <defs>
                                    <linearGradient id="solutionGrad1" x1="0" y1="0" x2="160" y2="110">
                                        <stop offset="0%" stop-color="#0F62FE"/>
                                        <stop offset="100%" stop-color="#3B82F6"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <div class="solution-content">
                            <h3>
                                <span data-en="Startups & SMBs" data-ar="الشركات الناشئة والصغيرة">Startups & SMBs</span>
                            </h3>
                            <p>
                                <span data-en="Launch faster with our streamlined deployment pipeline. Perfect for teams that need to move quickly without compromising on quality." data-ar="أطلق بشكل أسرع مع خط الإنتاج المبسط. مثالي للفرق التي تحتاج للحركة السريعة دون التنازل عن الجودة.">Launch faster with our streamlined deployment pipeline. Perfect for teams that need to move quickly without compromising on quality.</span>
                            </p>
                            <ul class="solution-features">
                                <li><span data-en="Quick setup in minutes" data-ar="إعداد سريع في دقائق">Quick setup in minutes</span></li>
                                <li><span data-en="Cost-effective scaling" data-ar="توسع فعال من ناحية التكلفة">Cost-effective scaling</span></li>
                                <li><span data-en="24/7 support" data-ar="دعم على مدار الساعة">24/7 support</span></li>
                            </ul>
                        </div>
                    </div>

                    <div class="solution-card">
                        <div class="solution-image">
                            <svg viewBox="0 0 200 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="200" height="150" rx="8" fill="#f8fafc"/>
                                <rect x="20" y="20" width="160" height="110" rx="4" fill="url(#solutionGrad2)"/>
                                <rect x="40" y="40" width="120" height="8" rx="4" fill="white" opacity="0.8"/>
                                <rect x="40" y="60" width="80" height="8" rx="4" fill="white" opacity="0.6"/>
                                <rect x="40" y="80" width="100" height="8" rx="4" fill="white" opacity="0.9"/>
                                <rect x="40" y="100" width="60" height="8" rx="4" fill="white" opacity="0.7"/>
                                <defs>
                                    <linearGradient id="solutionGrad2" x1="0" y1="0" x2="160" y2="110">
                                        <stop offset="0%" stop-color="#22C55E"/>
                                        <stop offset="100%" stop-color="#16A34A"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <div class="solution-content">
                            <h3>
                                <span data-en="Enterprise Teams" data-ar="فرق المؤسسات">Enterprise Teams</span>
                            </h3>
                            <p>
                                <span data-en="Advanced security, compliance, and governance features for large-scale operations. Built for teams that demand enterprise-grade reliability." data-ar="ميزات أمان وامتثال وحوكمة متقدمة للعمليات واسعة النطاق. مصمم للفرق التي تتطلب موثوقية على مستوى المؤسسات.">Advanced security, compliance, and governance features for large-scale operations. Built for teams that demand enterprise-grade reliability.</span>
                            </p>
                            <ul class="solution-features">
                                <li><span data-en="SOC 2 & ISO compliance" data-ar="امتثال SOC 2 و ISO">SOC 2 & ISO compliance</span></li>
                                <li><span data-en="Advanced analytics" data-ar="تحليلات متقدمة">Advanced analytics</span></li>
                                <li><span data-en="Dedicated support" data-ar="دعم مخصص">Dedicated support</span></li>
                            </ul>
                        </div>
                    </div>

                    <div class="solution-card">
                        <div class="solution-image">
                            <svg viewBox="0 0 200 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="200" height="150" rx="8" fill="#f8fafc"/>
                                <rect x="20" y="20" width="160" height="110" rx="4" fill="url(#solutionGrad3)"/>
                                <path d="M60 50L80 70L120 50L140 70L120 90L80 90L60 70Z" fill="white" opacity="0.8"/>
                                <circle cx="100" cy="70" r="8" fill="white"/>
                                <defs>
                                    <linearGradient id="solutionGrad3" x1="0" y1="0" x2="160" y2="110">
                                        <stop offset="0%" stop-color="#8B5CF6"/>
                                        <stop offset="100%" stop-color="#7C3AED"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <div class="solution-content">
                            <h3>
                                <span data-en="Agencies & Freelancers" data-ar="الوكالات والمستقلين">Agencies & Freelancers</span>
                            </h3>
                            <p>
                                <span data-en="Manage multiple client projects with ease. White-label solutions and client management tools to grow your business efficiently." data-ar="إدارة مشاريع عملاء متعددة بسهولة. حلول العلامة البيضاء وأدوات إدارة العملاء لتنمية عملك بكفاءة.">Manage multiple client projects with ease. White-label solutions and client management tools to grow your business efficiently.</span>
                            </p>
                            <ul class="solution-features">
                                <li><span data-en="Multi-client dashboard" data-ar="لوحة تحكم متعددة العملاء">Multi-client dashboard</span></li>
                                <li><span data-en="White-label options" data-ar="خيارات العلامة البيضاء">White-label options</span></li>
                                <li><span data-en="Billing integration" data-ar="تكامل الفوترة">Billing integration</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="stats" aria-labelledby="stats-title">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" data-en="50K+" data-ar="50K+">50K+</div>
                        <div class="stat-label" data-en="Developers Trust Us" data-ar="مطور يثق بنا">Developers Trust Us</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-en="99.9%" data-ar="99.9%">99.9%</div>
                        <div class="stat-label" data-en="Uptime Guarantee" data-ar="ضمان وقت التشغيل">Uptime Guarantee</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-en="2M+" data-ar="2M+">2M+</div>
                        <div class="stat-label" data-en="Deployments Monthly" data-ar="عملية نشر شهرياً">Deployments Monthly</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-en="150+" data-ar="150+">150+</div>
                        <div class="stat-label" data-en="Countries Served" data-ar="دولة نخدمها">Countries Served</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="testimonials" id="testimonials" aria-labelledby="testimonials-title">
            <div class="container">
                <h2 id="testimonials-title" class="section-title">
                    <span data-en="What Our Customers Say" data-ar="ماذا يقول عملاؤنا">What Our Customers Say</span>
                </h2>
                <div class="testimonials-grid">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="stars">⭐⭐⭐⭐⭐</div>
                            <p>
                                <span data-en="TechFlow transformed our deployment process. What used to take hours now takes minutes. The AI-powered optimizations are incredible!" data-ar="تك فلو غيّر عملية النشر لدينا. ما كان يستغرق ساعات أصبح يستغرق دقائق. التحسينات المدعومة بالذكاء الاصطناعي مذهلة!">TechFlow transformed our deployment process. What used to take hours now takes minutes. The AI-powered optimizations are incredible!</span>
                            </p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="20" cy="20" r="20" fill="#0F62FE"/>
                                    <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-weight="bold">SA</text>
                                </svg>
                            </div>
                            <div class="author-info">
                                <div class="author-name" data-en="Sarah Ahmed" data-ar="سارة أحمد">Sarah Ahmed</div>
                                <div class="author-title" data-en="CTO, TechStart Inc." data-ar="مدير التقنية، تك ستارت">CTO, TechStart Inc.</div>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="stars">⭐⭐⭐⭐⭐</div>
                            <p>
                                <span data-en="The security features and compliance tools gave us confidence to migrate our entire infrastructure. Outstanding support team!" data-ar="ميزات الأمان وأدوات الامتثال أعطتنا الثقة لنقل بنيتنا التحتية بالكامل. فريق دعم متميز!">The security features and compliance tools gave us confidence to migrate our entire infrastructure. Outstanding support team!</span>
                            </p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="20" cy="20" r="20" fill="#22C55E"/>
                                    <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-weight="bold">MK</text>
                                </svg>
                            </div>
                            <div class="author-info">
                                <div class="author-name" data-en="Michael Kim" data-ar="مايكل كيم">Michael Kim</div>
                                <div class="author-title" data-en="DevOps Lead, Enterprise Corp" data-ar="قائد DevOps، إنتربرايز كورب">DevOps Lead, Enterprise Corp</div>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="stars">⭐⭐⭐⭐⭐</div>
                            <p>
                                <span data-en="As a freelancer managing multiple client projects, TechFlow's multi-client dashboard is a game-changer. Highly recommended!" data-ar="كمستقل أدير مشاريع عملاء متعددة، لوحة تحكم تك فلو متعددة العملاء غيّرت قواعد اللعبة. أنصح بها بشدة!">As a freelancer managing multiple client projects, TechFlow's multi-client dashboard is a game-changer. Highly recommended!</span>
                            </p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="20" cy="20" r="20" fill="#8B5CF6"/>
                                    <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-weight="bold">LR</text>
                                </svg>
                            </div>
                            <div class="author-info">
                                <div class="author-name" data-en="Lisa Rodriguez" data-ar="ليزا رودريغيز">Lisa Rodriguez</div>
                                <div class="author-title" data-en="Full-Stack Developer" data-ar="مطور متكامل">Full-Stack Developer</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="how-it-works" aria-labelledby="how-title">
            <div class="container">
                <h2 id="how-title" class="section-title">
                    <span data-en="How It Works" data-ar="كيف يعمل">How It Works</span>
                </h2>
                <div class="steps-grid">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h3>
                            <span data-en="Connect Your Repository" data-ar="اربط مستودعك">Connect Your Repository</span>
                        </h3>
                        <p>
                            <span data-en="Link your GitHub, GitLab, or Bitbucket repository in seconds with our seamless integration." data-ar="اربط مستودع GitHub أو GitLab أو Bitbucket في ثوانٍ مع تكاملنا السلس.">Link your GitHub, GitLab, or Bitbucket repository in seconds with our seamless integration.</span>
                        </p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h3>
                            <span data-en="Configure & Deploy" data-ar="اضبط وانشر">Configure & Deploy</span>
                        </h3>
                        <p>
                            <span data-en="Our AI automatically detects your framework and configures optimal deployment settings." data-ar="ذكاؤنا الاصطناعي يكتشف إطار عملك تلقائياً ويضبط إعدادات النشر المثلى.">Our AI automatically detects your framework and configures optimal deployment settings.</span>
                        </p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h3>
                            <span data-en="Scale & Monitor" data-ar="وسّع وراقب">Scale & Monitor</span>
                        </h3>
                        <p>
                            <span data-en="Watch your application scale automatically with real-time monitoring and performance insights." data-ar="شاهد تطبيقك يتوسع تلقائياً مع مراقبة فورية ورؤى الأداء.">Watch your application scale automatically with real-time monitoring and performance insights.</span>
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <section class="pricing" id="pricing" aria-labelledby="pricing-title">
            <div class="container">
                <h2 id="pricing-title" class="section-title">
                    <span data-en="Simple, Transparent Pricing" data-ar="تسعير بسيط وشفاف">Simple, Transparent Pricing</span>
                </h2>
                <div class="pricing-grid">
                    <div class="pricing-card">
                        <h3>
                            <span data-en="Starter" data-ar="المبتدئ">Starter</span>
                        </h3>
                        <div class="price">
                            <span data-en="$9" data-ar="$9">$9</span>
                            <span class="period" data-en="/month" data-ar="/شهر">/month</span>
                        </div>
                        <ul class="features-list">
                            <li><span data-en="5 Projects" data-ar="5 مشاريع">5 Projects</span></li>
                            <li><span data-en="100GB Bandwidth" data-ar="100 جيجا نقل">100GB Bandwidth</span></li>
                            <li><span data-en="Email Support" data-ar="دعم بريد إلكتروني">Email Support</span></li>
                        </ul>
                        <a href="#contact" class="btn btn-secondary">
                            <span data-en="Get Started" data-ar="ابدأ الآن">Get Started</span>
                        </a>
                    </div>
                    <div class="pricing-card featured">
                        <div class="popular-badge">
                            <span data-en="Most Popular" data-ar="الأكثر شعبية">Most Popular</span>
                        </div>
                        <h3>
                            <span data-en="Professional" data-ar="المحترف">Professional</span>
                        </h3>
                        <div class="price">
                            <span data-en="$29" data-ar="$29">$29</span>
                            <span class="period" data-en="/month" data-ar="/شهر">/month</span>
                        </div>
                        <ul class="features-list">
                            <li><span data-en="Unlimited Projects" data-ar="مشاريع غير محدودة">Unlimited Projects</span></li>
                            <li><span data-en="1TB Bandwidth" data-ar="1 تيرا نقل">1TB Bandwidth</span></li>
                            <li><span data-en="Priority Support" data-ar="دعم أولوية">Priority Support</span></li>
                            <li><span data-en="Advanced Analytics" data-ar="تحليلات متقدمة">Advanced Analytics</span></li>
                        </ul>
                        <a href="#contact" class="btn btn-primary">
                            <span data-en="Start Free Trial" data-ar="ابدأ تجربة مجانية">Start Free Trial</span>
                        </a>
                    </div>
                    <div class="pricing-card">
                        <h3>
                            <span data-en="Enterprise" data-ar="المؤسسات">Enterprise</span>
                        </h3>
                        <div class="price">
                            <span data-en="Custom" data-ar="مخصص">Custom</span>
                        </div>
                        <ul class="features-list">
                            <li><span data-en="Custom Solutions" data-ar="حلول مخصصة">Custom Solutions</span></li>
                            <li><span data-en="Dedicated Support" data-ar="دعم مخصص">Dedicated Support</span></li>
                            <li><span data-en="SLA Guarantee" data-ar="ضمان SLA">SLA Guarantee</span></li>
                            <li><span data-en="On-premise Option" data-ar="خيار محلي">On-premise Option</span></li>
                        </ul>
                        <a href="#contact" class="btn btn-secondary">
                            <span data-en="Contact Sales" data-ar="اتصل بالمبيعات">Contact Sales</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <section class="integrations" aria-labelledby="integrations-title">
            <div class="container">
                <h2 id="integrations-title" class="section-title">
                    <span data-en="Seamless Integrations" data-ar="تكامل سلس">Seamless Integrations</span>
                </h2>
                <p class="section-subtitle">
                    <span data-en="Connect with your favorite tools and services" data-ar="اتصل بأدواتك وخدماتك المفضلة">Connect with your favorite tools and services</span>
                </p>
                <div class="integrations-grid">
                    <div class="integration-item">
                        <div class="integration-icon">
                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#24292e"/>
                                <path d="M20 8C13.37 8 8 13.37 8 20C8 25.31 11.435 29.795 16.205 31.385C16.805 31.49 17.03 31.13 17.03 30.815C17.03 30.53 17.015 29.585 17.015 28.58C14 29.135 13.22 27.845 12.98 27.17C12.845 26.825 12.26 25.76 11.75 25.475C11.33 25.25 10.73 24.695 11.735 24.68C12.68 24.665 13.355 25.55 13.58 25.91C14.66 27.725 16.385 27.215 17.075 26.9C17.18 26.12 17.495 25.595 17.84 25.295C15.17 24.995 12.38 23.96 12.38 19.37C12.38 18.065 12.845 16.985 13.61 16.145C13.49 15.845 13.07 14.615 13.73 12.965C13.73 12.965 14.735 12.65 17.03 14.195C17.99 13.925 19.01 13.79 20.03 13.79C21.05 13.79 22.07 13.925 23.03 14.195C25.325 12.635 26.33 12.965 26.33 12.965C26.99 14.615 26.57 15.845 26.45 16.145C27.215 16.985 27.68 18.05 27.68 19.37C27.68 23.975 24.875 24.995 22.205 25.295C22.64 25.67 23.015 26.39 23.015 27.515C23.015 29.12 23 30.415 23 30.815C23 31.13 23.225 31.505 23.825 31.385C28.565 29.795 32 25.295 32 20C32 13.37 26.63 8 20 8Z" fill="white"/>
                            </svg>
                        </div>
                        <span data-en="GitHub" data-ar="GitHub">GitHub</span>
                    </div>
                    <div class="integration-item">
                        <div class="integration-icon">
                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#FC6D26"/>
                                <path d="M20 30L12 22L14.5 12H25.5L28 22L20 30Z" fill="white"/>
                            </svg>
                        </div>
                        <span data-en="GitLab" data-ar="GitLab">GitLab</span>
                    </div>
                    <div class="integration-item">
                        <div class="integration-icon">
                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#0052CC"/>
                                <circle cx="20" cy="20" r="8" fill="white"/>
                                <circle cx="20" cy="20" r="4" fill="#0052CC"/>
                            </svg>
                        </div>
                        <span data-en="Bitbucket" data-ar="Bitbucket">Bitbucket</span>
                    </div>
                    <div class="integration-item">
                        <div class="integration-icon">
                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#FF6900"/>
                                <rect x="8" y="16" width="24" height="8" rx="2" fill="white"/>
                            </svg>
                        </div>
                        <span data-en="Docker" data-ar="Docker">Docker</span>
                    </div>
                    <div class="integration-item">
                        <div class="integration-icon">
                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#326CE5"/>
                                <path d="M20 8L28 14V26L20 32L12 26V14L20 8Z" fill="white"/>
                            </svg>
                        </div>
                        <span data-en="Kubernetes" data-ar="Kubernetes">Kubernetes</span>
                    </div>
                    <div class="integration-item">
                        <div class="integration-icon">
                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#FF9900"/>
                                <path d="M8 20L20 8L32 20L20 32L8 20Z" fill="white"/>
                            </svg>
                        </div>
                        <span data-en="AWS" data-ar="AWS">AWS</span>
                    </div>
                    <div class="integration-item">
                        <div class="integration-icon">
                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#4285F4"/>
                                <path d="M12 12H28V28H12V12Z" fill="white"/>
                                <path d="M16 16H24V24H16V16Z" fill="#4285F4"/>
                            </svg>
                        </div>
                        <span data-en="Google Cloud" data-ar="Google Cloud">Google Cloud</span>
                    </div>
                    <div class="integration-item">
                        <div class="integration-icon">
                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#0078D4"/>
                                <rect x="8" y="8" width="24" height="24" rx="4" fill="white"/>
                                <rect x="12" y="12" width="16" height="16" rx="2" fill="#0078D4"/>
                            </svg>
                        </div>
                        <span data-en="Azure" data-ar="Azure">Azure</span>
                    </div>
                </div>
            </div>
        </section>

        <section class="resources" id="resources" aria-labelledby="resources-title">
            <div class="container">
                <h2 id="resources-title" class="section-title">
                    <span data-en="Resources & Learning" data-ar="الموارد والتعلم">Resources & Learning</span>
                </h2>
                <div class="resources-grid">
                    <div class="resource-card">
                        <div class="resource-icon">📚</div>
                        <h3>
                            <span data-en="Documentation" data-ar="التوثيق">Documentation</span>
                        </h3>
                        <p>
                            <span data-en="Comprehensive guides and API references to get you started quickly." data-ar="أدلة شاملة ومراجع API لتبدأ بسرعة.">Comprehensive guides and API references to get you started quickly.</span>
                        </p>
                        <a href="#" class="resource-link">
                            <span data-en="Read Docs" data-ar="اقرأ التوثيق">Read Docs</span> →
                        </a>
                    </div>

                    <div class="resource-card">
                        <div class="resource-icon">🎥</div>
                        <h3>
                            <span data-en="Video Tutorials" data-ar="دروس فيديو">Video Tutorials</span>
                        </h3>
                        <p>
                            <span data-en="Step-by-step video guides covering everything from basics to advanced features." data-ar="أدلة فيديو خطوة بخطوة تغطي كل شيء من الأساسيات إلى الميزات المتقدمة.">Step-by-step video guides covering everything from basics to advanced features.</span>
                        </p>
                        <a href="#" class="resource-link">
                            <span data-en="Watch Videos" data-ar="شاهد الفيديوهات">Watch Videos</span> →
                        </a>
                    </div>

                    <div class="resource-card">
                        <div class="resource-icon">💬</div>
                        <h3>
                            <span data-en="Community Forum" data-ar="منتدى المجتمع">Community Forum</span>
                        </h3>
                        <p>
                            <span data-en="Connect with other developers, share experiences, and get help from the community." data-ar="تواصل مع مطورين آخرين، شارك التجارب، واحصل على المساعدة من المجتمع.">Connect with other developers, share experiences, and get help from the community.</span>
                        </p>
                        <a href="#" class="resource-link">
                            <span data-en="Join Forum" data-ar="انضم للمنتدى">Join Forum</span> →
                        </a>
                    </div>

                    <div class="resource-card">
                        <div class="resource-icon">📝</div>
                        <h3>
                            <span data-en="Blog & Updates" data-ar="المدونة والتحديثات">Blog & Updates</span>
                        </h3>
                        <p>
                            <span data-en="Stay updated with the latest features, best practices, and industry insights." data-ar="ابق محدثاً بأحدث الميزات وأفضل الممارسات ورؤى الصناعة.">Stay updated with the latest features, best practices, and industry insights.</span>
                        </p>
                        <a href="#" class="resource-link">
                            <span data-en="Read Blog" data-ar="اقرأ المدونة">Read Blog</span> →
                        </a>
                    </div>

                    <div class="resource-card">
                        <div class="resource-icon">🛠️</div>
                        <h3>
                            <span data-en="Developer Tools" data-ar="أدوات المطور">Developer Tools</span>
                        </h3>
                        <p>
                            <span data-en="CLI tools, SDKs, and plugins to integrate TechFlow into your workflow." data-ar="أدوات CLI وSDKs والإضافات لدمج تك فلو في سير عملك.">CLI tools, SDKs, and plugins to integrate TechFlow into your workflow.</span>
                        </p>
                        <a href="#" class="resource-link">
                            <span data-en="Get Tools" data-ar="احصل على الأدوات">Get Tools</span> →
                        </a>
                    </div>

                    <div class="resource-card">
                        <div class="resource-icon">🎓</div>
                        <h3>
                            <span data-en="Certification" data-ar="الشهادات">Certification</span>
                        </h3>
                        <p>
                            <span data-en="Become a certified TechFlow expert and showcase your skills to employers." data-ar="كن خبيراً معتمداً في تك فلو واعرض مهاراتك لأصحاب العمل.">Become a certified TechFlow expert and showcase your skills to employers.</span>
                        </p>
                        <a href="#" class="resource-link">
                            <span data-en="Get Certified" data-ar="احصل على الشهادة">Get Certified</span> →
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <section class="faq" id="faq" aria-labelledby="faq-title">
            <div class="container">
                <h2 id="faq-title" class="section-title">
                    <span data-en="Frequently Asked Questions" data-ar="الأسئلة الشائعة">Frequently Asked Questions</span>
                </h2>
                <div class="faq-list">
                    <div class="faq-item">
                        <button class="faq-question" aria-expanded="false">
                            <span data-en="How quickly can I deploy my first application?" data-ar="كم من الوقت يستغرق نشر تطبيقي الأول؟">How quickly can I deploy my first application?</span>
                            <span class="faq-icon" aria-hidden="true">+</span>
                        </button>
                        <div class="faq-answer">
                            <p>
                                <span data-en="Most applications can be deployed within 2-3 minutes of connecting your repository. Our AI automatically detects your framework and configures the optimal deployment settings." data-ar="يمكن نشر معظم التطبيقات خلال 2-3 دقائق من ربط مستودعك. ذكاؤنا الاصطناعي يكتشف إطار عملك تلقائياً ويضبط إعدادات النشر المثلى.">Most applications can be deployed within 2-3 minutes of connecting your repository. Our AI automatically detects your framework and configures the optimal deployment settings.</span>
                            </p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <button class="faq-question" aria-expanded="false">
                            <span data-en="What frameworks and languages do you support?" data-ar="ما هي الأُطر واللغات التي تدعمونها؟">What frameworks and languages do you support?</span>
                            <span class="faq-icon" aria-hidden="true">+</span>
                        </button>
                        <div class="faq-answer">
                            <p>
                                <span data-en="We support all major frameworks including React, Vue, Angular, Next.js, Nuxt.js, Svelte, and many more. Backend support includes Node.js, Python, Go, Ruby, PHP, and .NET." data-ar="ندعم جميع الأُطر الرئيسية بما في ذلك React و Vue و Angular و Next.js و Nuxt.js و Svelte وغيرها الكثير. دعم الخلفية يشمل Node.js و Python و Go و Ruby و PHP و .NET.">We support all major frameworks including React, Vue, Angular, Next.js, Nuxt.js, Svelte, and many more. Backend support includes Node.js, Python, Go, Ruby, PHP, and .NET.</span>
                            </p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <button class="faq-question" aria-expanded="false">
                            <span data-en="Is there a free trial available?" data-ar="هل تتوفر تجربة مجانية؟">Is there a free trial available?</span>
                            <span class="faq-icon" aria-hidden="true">+</span>
                        </button>
                        <div class="faq-answer">
                            <p>
                                <span data-en="Yes! We offer a 14-day free trial with full access to all Professional features. No credit card required to start." data-ar="نعم! نقدم تجربة مجانية لمدة 14 يوماً مع وصول كامل لجميع ميزات الخطة المحترفة. لا حاجة لبطاقة ائتمان للبدء.">Yes! We offer a 14-day free trial with full access to all Professional features. No credit card required to start.</span>
                            </p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <button class="faq-question" aria-expanded="false">
                            <span data-en="How secure is my code and data?" data-ar="ما مدى أمان كودي وبياناتي؟">How secure is my code and data?</span>
                            <span class="faq-icon" aria-hidden="true">+</span>
                        </button>
                        <div class="faq-answer">
                            <p>
                                <span data-en="We use bank-grade security with end-to-end encryption, SOC 2 compliance, and regular security audits. Your code is never stored permanently and is encrypted in transit and at rest." data-ar="نستخدم أماناً بمستوى البنوك مع تشفير شامل وامتثال SOC 2 وتدقيق أمني منتظم. كودك لا يُحفظ بشكل دائم ومشفر أثناء النقل والتخزين.">We use bank-grade security with end-to-end encryption, SOC 2 compliance, and regular security audits. Your code is never stored permanently and is encrypted in transit and at rest.</span>
                            </p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <button class="faq-question" aria-expanded="false">
                            <span data-en="Can I cancel my subscription anytime?" data-ar="هل يمكنني إلغاء اشتراكي في أي وقت؟">Can I cancel my subscription anytime?</span>
                            <span class="faq-icon" aria-hidden="true">+</span>
                        </button>
                        <div class="faq-answer">
                            <p>
                                <span data-en="Absolutely! You can cancel your subscription at any time with no cancellation fees. Your service will continue until the end of your current billing period." data-ar="بالطبع! يمكنك إلغاء اشتراكك في أي وقت بدون رسوم إلغاء. ستستمر خدمتك حتى نهاية فترة الفوترة الحالية.">Absolutely! You can cancel your subscription at any time with no cancellation fees. Your service will continue until the end of your current billing period.</span>
                            </p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <button class="faq-question" aria-expanded="false">
                            <span data-en="Do you offer customer support?" data-ar="هل تقدمون دعم العملاء؟">Do you offer customer support?</span>
                            <span class="faq-icon" aria-hidden="true">+</span>
                        </button>
                        <div class="faq-answer">
                            <p>
                                <span data-en="Yes! We provide 24/7 email support for all plans, with priority support for Professional and Enterprise customers. Enterprise customers also get dedicated account management." data-ar="نعم! نقدم دعم بريد إلكتروني على مدار الساعة لجميع الخطط، مع دعم أولوية لعملاء المحترف والمؤسسات. عملاء المؤسسات يحصلون أيضاً على إدارة حساب مخصصة.">Yes! We provide 24/7 email support for all plans, with priority support for Professional and Enterprise customers. Enterprise customers also get dedicated account management.</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="contact" id="contact" aria-labelledby="contact-title">
            <div class="container">
                <div class="contact-content">
                    <div class="contact-info">
                        <h2 id="contact-title">
                            <span data-en="Ready to Get Started?" data-ar="مستعد للبدء؟">Ready to Get Started?</span>
                        </h2>
                        <p>
                            <span data-en="Join thousands of developers who trust TechFlow to deploy their applications faster and more reliably." data-ar="انضم لآلاف المطورين الذين يثقون في تك فلو لنشر تطبيقاتهم بشكل أسرع وأكثر موثوقية.">Join thousands of developers who trust TechFlow to deploy their applications faster and more reliably.</span>
                        </p>
                        <div class="contact-methods">
                            <a href="mailto:<EMAIL>" class="contact-method">
                                <span class="contact-icon">📧</span>
                                <span data-en="<EMAIL>" data-ar="<EMAIL>"><EMAIL></span>
                            </a>
                            <a href="tel:+15550123" class="contact-method">
                                <span class="contact-icon">📞</span>
                                <span data-en="+1 (555) 0123" data-ar="+1 (555) 0123">+1 (555) 0123</span>
                            </a>
                            <a href="https://wa.me/15550123" class="contact-method" target="_blank" rel="noopener">
                                <span class="contact-icon">💬</span>
                                <span data-en="WhatsApp Support" data-ar="دعم واتساب">WhatsApp Support</span>
                            </a>
                        </div>
                    </div>
                    <form class="contact-form" id="contactForm" novalidate>
                        <div class="form-group">
                            <label for="name">
                                <span data-en="Full Name *" data-ar="الاسم الكامل *">Full Name *</span>
                            </label>
                            <input type="text" id="name" name="name" required aria-describedby="name-error">
                            <div class="error-message" id="name-error" role="alert"></div>
                        </div>
                        <div class="form-group">
                            <label for="email">
                                <span data-en="Email Address *" data-ar="البريد الإلكتروني *">Email Address *</span>
                            </label>
                            <input type="email" id="email" name="email" required aria-describedby="email-error">
                            <div class="error-message" id="email-error" role="alert"></div>
                        </div>
                        <div class="form-group">
                            <label for="phone">
                                <span data-en="Phone Number" data-ar="رقم الهاتف">Phone Number</span>
                            </label>
                            <input type="tel" id="phone" name="phone" aria-describedby="phone-error">
                            <div class="error-message" id="phone-error" role="alert"></div>
                        </div>
                        <div class="form-group">
                            <label for="company">
                                <span data-en="Company" data-ar="الشركة">Company</span>
                            </label>
                            <input type="text" id="company" name="company">
                        </div>
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="consent" name="consent" required>
                                <span class="checkmark"></span>
                                <span data-en="I agree to receive marketing communications and accept the Privacy Policy *" data-ar="أوافق على تلقي الاتصالات التسويقية وأقبل سياسة الخصوصية *">I agree to receive marketing communications and accept the Privacy Policy *</span>
                            </label>
                            <div class="error-message" id="consent-error" role="alert"></div>
                        </div>
                        <button type="submit" class="btn btn-primary btn-full" id="submitBtn">
                            <span data-en="Start Free Trial" data-ar="ابدأ تجربة مجانية">Start Free Trial</span>
                        </button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>
                        <span data-en="TechFlow" data-ar="تك فلو">TechFlow</span>
                    </h3>
                    <p>
                        <span data-en="Accelerating development workflows worldwide." data-ar="تسريع سير عمل التطوير عالمياً.">Accelerating development workflows worldwide.</span>
                    </p>
                </div>
                <div class="footer-section">
                    <h4>
                        <span data-en="Product" data-ar="المنتج">Product</span>
                    </h4>
                    <ul>
                        <li><a href="#features"><span data-en="Features" data-ar="الميزات">Features</span></a></li>
                        <li><a href="#pricing"><span data-en="Pricing" data-ar="التسعير">Pricing</span></a></li>
                        <li><a href="#"><span data-en="Documentation" data-ar="التوثيق">Documentation</span></a></li>
                        <li><a href="#"><span data-en="API" data-ar="واجهة برمجة التطبيقات">API</span></a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>
                        <span data-en="Company" data-ar="الشركة">Company</span>
                    </h4>
                    <ul>
                        <li><a href="#"><span data-en="About" data-ar="حولنا">About</span></a></li>
                        <li><a href="#"><span data-en="Blog" data-ar="المدونة">Blog</span></a></li>
                        <li><a href="#"><span data-en="Careers" data-ar="الوظائف">Careers</span></a></li>
                        <li><a href="#contact"><span data-en="Contact" data-ar="اتصل بنا">Contact</span></a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>
                        <span data-en="Legal" data-ar="قانوني">Legal</span>
                    </h4>
                    <ul>
                        <li><a href="#"><span data-en="Privacy Policy" data-ar="سياسة الخصوصية">Privacy Policy</span></a></li>
                        <li><a href="#"><span data-en="Terms of Service" data-ar="شروط الخدمة">Terms of Service</span></a></li>
                        <li><a href="#"><span data-en="Cookie Policy" data-ar="سياسة الكوكيز">Cookie Policy</span></a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 TechFlow. <span data-en="All rights reserved." data-ar="جميع الحقوق محفوظة.">All rights reserved.</span></p>
            </div>
        </div>
    </footer>

    <!-- Cookie Consent Banner -->
    <div class="cookie-banner" id="cookieBanner">
        <div class="cookie-content">
            <p>
                <span data-en="We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies." data-ar="نستخدم الكوكيز لتحسين تجربتك. بمتابعة زيارة هذا الموقع فإنك توافق على استخدامنا للكوكيز.">We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</span>
            </p>
            <div class="cookie-actions">
                <button class="btn btn-secondary btn-sm" id="acceptCookies">
                    <span data-en="Accept" data-ar="موافق">Accept</span>
                </button>
                <a href="#" class="cookie-link">
                    <span data-en="Learn More" data-ar="اعرف المزيد">Learn More</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Success Toast -->
    <div class="toast" id="successToast" role="alert" aria-live="polite">
        <div class="toast-content">
            <span class="toast-icon">✅</span>
            <span class="toast-message" data-en="Thank you! We'll be in touch soon." data-ar="شكراً لك! سنتواصل معك قريباً.">Thank you! We'll be in touch soon.</span>
        </div>
    </div>

    <script defer>
        // Language and Direction Toggle
        const langToggle = document.getElementById('langToggle');
        const html = document.documentElement;
        let currentLang = 'en';

        // FAQ Accordion functionality
        function initFAQ() {
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', () => {
                    const isExpanded = question.getAttribute('aria-expanded') === 'true';
                    const answer = question.nextElementSibling;

                    // Close all other FAQ items
                    faqQuestions.forEach(q => {
                        if (q !== question) {
                            q.setAttribute('aria-expanded', 'false');
                            q.nextElementSibling.classList.remove('open');
                        }
                    });

                    // Toggle current item
                    question.setAttribute('aria-expanded', !isExpanded);
                    answer.classList.toggle('open', !isExpanded);
                });
            });
        }

        // Form validation and submission
        function initContactForm() {
            const form = document.getElementById('contactForm');
            const submitBtn = document.getElementById('submitBtn');
            const toast = document.getElementById('successToast');

            // Validation patterns
            const patterns = {
                name: /^[a-zA-Z\u0600-\u06FF\s]{2,50}$/,
                email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                phone: /^[\+]?[0-9\s\-\(\)]{10,20}$/
            };

            // Error messages
            const errorMessages = {
                en: {
                    name: 'Please enter a valid name (2-50 characters)',
                    email: 'Please enter a valid email address',
                    phone: 'Please enter a valid phone number',
                    consent: 'You must agree to the privacy policy'
                },
                ar: {
                    name: 'يرجى إدخال اسم صحيح (2-50 حرف)',
                    email: 'يرجى إدخال بريد إلكتروني صحيح',
                    phone: 'يرجى إدخال رقم هاتف صحيح',
                    consent: 'يجب الموافقة على سياسة الخصوصية'
                }
            };

            function validateField(field) {
                const value = field.value.trim();
                const name = field.name;
                const errorElement = document.getElementById(`${name}-error`);
                let isValid = true;

                if (name === 'consent') {
                    isValid = field.checked;
                } else if (field.required && !value) {
                    isValid = false;
                } else if (value && patterns[name] && !patterns[name].test(value)) {
                    isValid = false;
                }

                if (!isValid) {
                    field.classList.add('error');
                    if (errorElement) {
                        errorElement.textContent = errorMessages[currentLang][name] || 'Invalid input';
                    }
                } else {
                    field.classList.remove('error');
                    if (errorElement) {
                        errorElement.textContent = '';
                    }
                }

                return isValid;
            }

            function validateForm() {
                const fields = form.querySelectorAll('input[required]');
                let isValid = true;

                fields.forEach(field => {
                    if (!validateField(field)) {
                        isValid = false;
                    }
                });

                submitBtn.disabled = !isValid;
                return isValid;
            }

            // Real-time validation
            form.addEventListener('input', validateForm);
            form.addEventListener('change', validateForm);

            // Form submission
            form.addEventListener('submit', (e) => {
                e.preventDefault();

                if (validateForm()) {
                    // Simulate form submission
                    submitBtn.disabled = true;
                    submitBtn.textContent = currentLang === 'en' ? 'Submitting...' : 'جاري الإرسال...';

                    setTimeout(() => {
                        // Show success toast
                        toast.classList.add('show');
                        form.reset();
                        submitBtn.disabled = false;
                        submitBtn.querySelector('span').textContent = currentLang === 'en' ? 'Start Free Trial' : 'ابدأ تجربة مجانية';

                        // Hide toast after 5 seconds
                        setTimeout(() => {
                            toast.classList.remove('show');
                        }, 5000);
                    }, 1500);
                }
            });

            // Initial validation
            validateForm();
        }

        // Cookie banner functionality
        function initCookieBanner() {
            const banner = document.getElementById('cookieBanner');
            const acceptBtn = document.getElementById('acceptCookies');

            // Show banner if not previously accepted
            if (!localStorage.getItem('cookiesAccepted')) {
                setTimeout(() => {
                    banner.classList.add('show');
                }, 2000);
            }

            acceptBtn.addEventListener('click', () => {
                localStorage.setItem('cookiesAccepted', 'true');
                banner.classList.remove('show');
            });
        }

        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'ar' : 'en';
            html.setAttribute('lang', currentLang);
            html.setAttribute('dir', currentLang === 'ar' ? 'rtl' : 'ltr');

            // Update all text elements
            document.querySelectorAll('[data-en]').forEach(element => {
                const enText = element.getAttribute('data-en');
                const arText = element.getAttribute('data-ar');
                element.textContent = currentLang === 'en' ? enText : arText;
            });

            // Update form button text if form is reset
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn && !submitBtn.disabled) {
                submitBtn.querySelector('span').textContent = currentLang === 'en' ? 'Start Free Trial' : 'ابدأ تجربة مجانية';
            }

            // Update toast message
            const toastMessage = document.querySelector('.toast-message');
            if (toastMessage) {
                toastMessage.textContent = currentLang === 'en' ? "Thank you! We'll be in touch soon." : 'شكراً لك! سنتواصل معك قريباً.';
            }

            // Store language preference
            localStorage.setItem('preferredLanguage', currentLang);
        }

        langToggle.addEventListener('click', toggleLanguage);

        // Sticky header on scroll
        const header = document.getElementById('header');
        let lastScrollY = window.scrollY;

        function handleScroll() {
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            lastScrollY = window.scrollY;
        }

        window.addEventListener('scroll', handleScroll, { passive: true });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile menu functionality
        function initMobileMenu() {
            const mobileToggle = document.getElementById('mobileMenuToggle');
            const navMenu = document.getElementById('navMenu');

            if (mobileToggle && navMenu) {
                mobileToggle.addEventListener('click', () => {
                    mobileToggle.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });

                // Close menu when clicking on links
                navMenu.addEventListener('click', (e) => {
                    if (e.target.classList.contains('nav-link')) {
                        mobileToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });

                // Close menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!mobileToggle.contains(e.target) && !navMenu.contains(e.target)) {
                        mobileToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });
            }
        }

        // Active navigation link highlighting
        function initActiveNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('section[id]');

            function updateActiveLink() {
                let current = '';

                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    const sectionHeight = section.offsetHeight;

                    if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('active');
                    }
                });
            }

            window.addEventListener('scroll', updateActiveLink, { passive: true });
            updateActiveLink(); // Initial call
        }

        // Enhanced scroll animations
        function initScrollAnimations() {
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');

                            // Animate stats numbers
                            if (entry.target.classList.contains('stats')) {
                                animateStats();
                            }
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                });

                // Observe sections for animation
                document.querySelectorAll('section, .solution-card, .testimonial-card, .resource-card').forEach(element => {
                    observer.observe(element);
                });
            }
        }

        // Animate statistics numbers
        function animateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');

            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));
                const suffix = finalValue.replace(/[\d]/g, '');

                if (numericValue && !stat.classList.contains('animated')) {
                    stat.classList.add('animated');
                    let currentValue = 0;
                    const increment = numericValue / 50;

                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= numericValue) {
                            currentValue = numericValue;
                            clearInterval(timer);
                        }
                        stat.textContent = Math.floor(currentValue) + suffix;
                    }, 30);
                }
            });
        }

        // Initialize all functionality
        function init() {
            // Load saved language preference
            const savedLang = localStorage.getItem('preferredLanguage');
            if (savedLang && savedLang !== currentLang) {
                toggleLanguage();
            }

            // Initialize components
            handleScroll();
            initFAQ();
            initContactForm();
            initCookieBanner();
            initMobileMenu();
            initActiveNavigation();
            initScrollAnimations();

            // Add loading class removal for better performance
            document.body.classList.add('loaded');
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }

        // Performance optimization for reduced motion
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--transition', 'none');
        }

        // Add smooth scroll behavior for better UX
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
</body>
</html>
